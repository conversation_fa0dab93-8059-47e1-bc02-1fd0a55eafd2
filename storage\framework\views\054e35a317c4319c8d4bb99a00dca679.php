
<?php $__env->startSection('admin_title'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="header pb-6 pt-5 pt-md-8">
      <div class="container-fluid">
        <div class="header-body">
            <h1 class="mb-3 mt--3">🌐 <?php echo e(__('Landing Page')); ?></h1>
          <div class="row align-items-center pt-2">
          </div>
        </div>
    </div>
    </div>
    <div class="container-fluid mt--6">
      <div class="row">
        <div class="col">
          <div class="card">
           
            <!-- Light table -->
            <div class="table-responsive">
              <table class="table align-items-center table-flush">
                <thead class="thead-light">
                  <tr>
                    <th scope="col" class="sort" data-sort="name"><?php echo e(__('Sections')); ?></th>
                    <th scope="col"></th>
                  </tr>
                </thead>
                <tbody class="list">
                    <?php $__currentLoopData = $sections; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <th scope="row">
                            <div class="media align-items-center">
                                <div class="media-body">
                                    <a href="<?php echo e(route('admin.landing.posts',['type'=>strtolower($section)])); ?>"><span class="name mb-0 text-sm"><?php echo e(__($key)); ?></span></a>
                                </div>
                            </div>
                        </th>
                        <td class="text-right">
                            
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
     
      </div>
    </div>
<?php $__env->stopSection(); ?>


<?php echo $__env->make('layouts.app', ['title' => __('Landing Page')], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\zaptra\resources\views/settings/landing/index.blade.php ENDPATH**/ ?>